package com.dorsey.core.dto.users;

import com.dorsey.core.dto.organization.HierarchyNodeDTO;
import com.dorsey.core.dto.roles.RoleCapabilitiesDTO;
import com.dorsey.core.dto.roles.RoleDTO;
import com.dorsey.core.dto.task.RoleWorkflowTaskXrefDTO;
import com.dorsey.core.model.user.Users;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public class User {
  private UUID userId;
  private String email;
  private Boolean isActive;
  private String firstName;
  private String lastName;
  private String title;
  private Integer hierarchyId;
  private String phone;
  private String mobile;
  private String address;
  private LocalDateTime effectiveDate;
  private LocalDateTime terminationDate;
  private byte[] profileImage;
  private Set<RoleDTO> roles;
  private Set<UserNotificationConfigDTO> notificationConfig;
  private HierarchyNodeDTO hierarchyData;
  private UserBillingInformationDTO billingInformation;

  public User(Users mod) {
    this.userId = mod.getUserId();
    this.email = mod.getEmail();
    this.isActive = mod.getIsActive();
    this.firstName = mod.getFirstName();
    this.lastName = mod.getLastName();
    this.title = mod.getTitle();
    this.hierarchyId = mod.getHierarchyId();
    this.effectiveDate = mod.getEffectiveDate();
    this.terminationDate = mod.getTerminationDate();
    this.profileImage = mod.getProfileImage();
    this.phone = mod.getContacts() != null ? mod.getContacts().getPhone() : null;
    this.mobile = mod.getContacts() != null ? mod.getContacts().getMobile() : null;
    this.address = mod.getContacts() != null ? mod.getContacts().getAddress() : null;

    var rolesDTO = new HashSet<RoleDTO>();

    mod.getRoles().forEach(r -> {
      List<RoleCapabilitiesDTO> capabilitiesDTO = new ArrayList<>();
      List<RoleWorkflowTaskXrefDTO> tasksDTO = new ArrayList<>();

      r.getRole().getCapabilities().forEach(c -> capabilitiesDTO.add(RoleCapabilitiesDTO.builder().roleId(c.getId().getRoleId()).view(c.getView()).create(c.getCreate()).edit(c.getEdit()).delete(c.getDelete()).build()));

          var dto = RoleDTO.builder()
              .roleId(r.getRole().getRoleId())
              .name(r.getRole().getName())
              .superiorId(r.getRole().getSuperiorId())
              .description(r.getRole().getDescription())
              .department(r.getRole().getDepartment())
              .capabilities(capabilitiesDTO)
              .tasks(tasksDTO)
              .build();
          dto.capabilitiesFromModel(r.getRole().getCapabilities());
          dto.tasksFromModel(r.getRole().getTasks());
          rolesDTO.add(dto);
        }
    );

    this.roles = rolesDTO;

    var userNotificationConfigDTO = new HashSet<UserNotificationConfigDTO>();

    // Handle null notificationConfig (e.g., for UserUtil.VISITOR)
    if (mod.getNotificationConfig() != null) {
      mod.getNotificationConfig().forEach(n -> userNotificationConfigDTO.add(
          UserNotificationConfigDTO.builder()
              .type(n.getId().getType())
              .objectId(n.getObjectId())
              .notify(n.getNotify()).build()));
    }

    this.notificationConfig = userNotificationConfigDTO;

    this.hierarchyData = mod.getHierarchyData() != null ?
        new HierarchyNodeDTO(mod.getHierarchyData()) : null;
  }
}
